def log_execution_time(file_type, operation, elapsed_time, log_path='processing_log.csv'):
    import csv
    import os

    # Prepare log entry
    log_entry = {}
    if file_type == 'proposal':
        if operation == 'summarization':
            log_entry['summarization_time'] = f"✅ Proposal Text Summarization completed in {elapsed_time} seconds"
        elif operation == 'extraction':
            log_entry['openai_extraction_time'] = f"✅ Proposal Text Extraction from Images completed in {elapsed_time} seconds"
    elif file_type == 'financial':
        if operation == 'summarization':
            log_entry['summarization_time'] = f"✅ Financial Text Summarization completed in {elapsed_time} seconds"

    # Write to CSV
    file_exists = os.path.isfile(log_path)
    with open(log_path, 'a', newline='') as csvfile:
        fieldnames = ['summarization_time', 'openai_extraction_time']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()
        writer.writerow(log_entry)

# Example usage:
# log_execution_time('proposal', 'summarization', 2.5)
# log_execution_time('financial', 'summarization', 1.8)
# log_execution_time('proposal', 'extraction', 3.2)